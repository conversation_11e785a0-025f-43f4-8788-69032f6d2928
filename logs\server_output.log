Server initialized for eventlet.
(2084) wsgi starting up on http://0.0.0.0:8000
(2084) accepted ('127.0.0.1', 57179)
127.0.0.1 - - [19/May/2025 23:18:48] "GET / HTTP/1.1" 302 696 0.003001
(2084) accepted ('127.0.0.1', 57181)
Starting application with Eventlet for Socket.IO support...
This is optimized for real-time communication.
Server will be available at http://localhost:8000
Press Ctrl+C to stop the server
2025-05-19 23:18:49,269 - mysql.connector - INFO - package: mysql.connector.plugins
2025-05-19 23:18:49,269 - mysql.connector - INFO - plugin_name: caching_sha2_password
2025-05-19 23:18:49,269 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
127.0.0.1 - - [19/May/2025 23:18:51] "GET /genius_page HTTP/1.1" 200 145126 3.349235
127.0.0.1 - - [19/May/2025 23:18:52] "GET /static/css/optimized-job-cards.css HTTP/1.1" 304 537 0.083006
127.0.0.1 - - [19/May/2025 23:18:52] "GET /static/img/giggenius_logo.jpg HTTP/1.1" 304 535 0.001003
(2084) accepted ('127.0.0.1', 57186)
(2084) accepted ('127.0.0.1', 57187)
127.0.0.1 - - [19/May/2025 23:18:53] "GET /api/profile-photo/client/2 HTTP/1.1" 200 1895179 1.715752
127.0.0.1 - - [19/May/2025 23:18:54] "GET /api/profile-photo/genius/3 HTTP/1.1" 200 2693174 1.957325
127.0.0.1 - - [19/May/2025 23:18:54] "GET /static/img/logo.png HTTP/1.1" 304 526 0.002000
127.0.0.1 - - [19/May/2025 23:18:58] "GET /messages HTTP/1.1" 200 157602 0.056005
127.0.0.1 - - [19/May/2025 23:18:58] "GET /static/img/giggenius_logo.jpg HTTP/1.1" 304 535 0.002003
127.0.0.1 - - [19/May/2025 23:18:58] "GET /static/img/default-avatar.png HTTP/1.1" 304 534 0.002006
XtrTFJEROl5wVfnJAAAA: Sending packet OPEN data {'sid': 'XtrTFJEROl5wVfnJAAAA', 'upgrades': ['websocket'], 'pingTimeout': 120000, 'pingInterval': 25000, 'maxPayload': 10000000.0}
Created new connection for thread 2027337826112. Active: 1
2025-05-19 23:18:58,717 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Sending packet OPEN data {'sid': 'XtrTFJEROl5wVfnJAAAA', 'upgrades': ['websocket'], 'pingTimeout': 120000, 'pingInterval': 25000, 'maxPayload': 10000000.0}
127.0.0.1 - - [19/May/2025 23:18:58] "GET /socket.io/?user_id=3&user_type=genius&t=PRf86sL&EIO=4&transport=polling HTTP/1.1" 200 304 0.001000
XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 0
2025-05-19 23:18:58,731 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 0
XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 0{"sid":"rk5P_6KpbR7KOTQoAAAB"}
2025-05-19 23:18:58,732 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 0{"sid":"rk5P_6KpbR7KOTQoAAAB"}
127.0.0.1 - - [19/May/2025 23:18:58] "POST /socket.io/?user_id=3&user_type=genius&t=PRf86sf&EIO=4&transport=polling&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 219 0.001000
127.0.0.1 - - [19/May/2025 23:18:58] "GET /socket.io/?user_id=3&user_type=genius&t=PRf86sg&EIO=4&transport=polling&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 213 0.000000
XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 2["join"]
2025-05-19 23:18:58,745 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 2["join"]
received event "join" from rk5P_6KpbR7KOTQoAAAB [/]
2025-05-19 23:18:58,745 - socketio.server - INFO - received event "join" from rk5P_6KpbR7KOTQoAAAB [/]
127.0.0.1 - - [19/May/2025 23:18:58] "POST /socket.io/?user_id=3&user_type=genius&t=PRf86ss&EIO=4&transport=polling&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 219 0.000996
rk5P_6KpbR7KOTQoAAAB is entering room 3 [/]
2025-05-19 23:18:58,746 - socketio.server - INFO - rk5P_6KpbR7KOTQoAAAB is entering room 3 [/]
rk5P_6KpbR7KOTQoAAAB is entering room rk5P_6KpbR7KOTQoAAAB [/]
User 3 (genius) joined room 3
2025-05-19 23:18:58,747 - socketio.server - INFO - rk5P_6KpbR7KOTQoAAAB is entering room rk5P_6KpbR7KOTQoAAAB [/]
emitting event "join_confirmation" to rk5P_6KpbR7KOTQoAAAB [/]
User 3 joined room rk5P_6KpbR7KOTQoAAAB
2025-05-19 23:18:58,747 - socketio.server - INFO - emitting event "join_confirmation" to rk5P_6KpbR7KOTQoAAAB [/]
XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["join_confirmation",{"user_id":3,"user_type":"genius","room":"3","sid":"rk5P_6KpbR7KOTQoAAAB"}]
2025-05-19 23:18:58,747 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["join_confirmation",{"user_id":3,"user_type":"genius","room":"3","sid":"rk5P_6KpbR7KOTQoAAAB"}]
127.0.0.1 - - [19/May/2025 23:18:58] "GET /socket.io/?user_id=3&user_type=genius&t=PRf86so&EIO=4&transport=polling&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 279 0.002002
(2084) accepted ('127.0.0.1', 57194)
XtrTFJEROl5wVfnJAAAA: Received request to upgrade to websocket
2025-05-19 23:18:59,038 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Received request to upgrade to websocket
127.0.0.1 - - [19/May/2025 23:18:59] "GET /socket.io/?user_id=3&user_type=genius&t=PRf86t6&EIO=4&transport=polling&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 181 0.280020
XtrTFJEROl5wVfnJAAAA: Upgrade to websocket successful
2025-05-19 23:18:59,042 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Upgrade to websocket successful
127.0.0.1 - - [19/May/2025 23:19:00] "GET /api/contacts HTTP/1.1" 200 663 2.063642
127.0.0.1 - - [19/May/2025 23:19:01] "GET /api/profile-photo/genius/3 HTTP/1.1" 200 2693174 2.682338
XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 2["mark_messages_read",{"contact_id":2}]
Created new connection for thread 2027338422592. Active: 2
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027337826112
2025-05-19 23:19:02,144 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Received packet MESSAGE data 2["mark_messages_read",{"contact_id":2}]
received event "mark_messages_read" from rk5P_6KpbR7KOTQoAAAB [/]
2025-05-19 23:19:02,144 - socketio.server - INFO - received event "mark_messages_read" from rk5P_6KpbR7KOTQoAAAB [/]
127.0.0.1 - - [19/May/2025 23:19:02] "GET /api/profile-photo/client/2 HTTP/1.1" 200 1895179 1.581656
127.0.0.1 - - [19/May/2025 23:19:03] "GET / HTTP/1.1" 302 696 0.000998
emitting event "messages_marked_read" to 3 [/]
Marking messages as read from 2 to 3
Created new connection for thread 2027337826112. Active: 2
Messages table exists: True
Total messages in database: 122
Created new connection for thread 2027338427008. Active: 3
Messages between users 3 and 2: 122
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338422592
Marked 0 messages as read
2025-05-19 23:19:03,705 - socketio.server - INFO - emitting event "messages_marked_read" to 3 [/]
XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
2025-05-19 23:19:03,705 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
emitting event "messages_marked_read" to rk5P_6KpbR7KOTQoAAAB [/]
2025-05-19 23:19:03,706 - socketio.server - INFO - emitting event "messages_marked_read" to rk5P_6KpbR7KOTQoAAAB [/]
XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
2025-05-19 23:19:03,706 - engineio.server - INFO - XtrTFJEROl5wVfnJAAAA: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
127.0.0.1 - - [19/May/2025 23:19:03] "GET /api/messages/2?limit=10&page=1 HTTP/1.1" 200 5506 2.905944
127.0.0.1 - - [19/May/2025 23:19:05] "GET /genius_page HTTP/1.1" 200 145126 2.251849
127.0.0.1 - - [19/May/2025 23:19:05] "GET /static/css/optimized-job-cards.css HTTP/1.1" 304 537 0.002001
127.0.0.1 - - [19/May/2025 23:19:05] "GET /static/img/giggenius_logo.jpg HTTP/1.1" 304 535 0.000999
(2084) accepted ('127.0.0.1', 57210)
(2084) accepted ('127.0.0.1', 57212)
127.0.0.1 - - [19/May/2025 23:19:05] "GET /api/files/786?t=1747667943748 HTTP/1.1" 200 26961 2.145494
127.0.0.1 - - [19/May/2025 23:19:06] "GET /api/files/792?t=1747667943749 HTTP/1.1" 200 26961 2.731705
127.0.0.1 - - [19/May/2025 23:19:06] "GET /socket.io/?user_id=3&user_type=genius&t=1747667938707&EIO=4&transport=websocket&sid=XtrTFJEROl5wVfnJAAAA HTTP/1.1" 200 0 7.548285
127.0.0.1 - - [19/May/2025 23:19:07] "GET /api/messages/batch/reactions?ids=785,786,787,788,789,790,791,792,793,794 HTTP/1.1" 200 683 2.921621
127.0.0.1 - - [19/May/2025 23:19:07] "GET /api/profile-photo/client/2 HTTP/1.1" 200 1895179 1.590348
127.0.0.1 - - [19/May/2025 23:19:07] "GET /messages HTTP/1.1" 200 157602 0.002000
127.0.0.1 - - [19/May/2025 23:19:07] "GET /static/img/giggenius_logo.jpg HTTP/1.1" 304 535 0.002002
127.0.0.1 - - [19/May/2025 23:19:07] "GET /static/img/default-avatar.png HTTP/1.1" 304 534 0.002003
EXIp1SjjbwsxFy6ZAAAC: Sending packet OPEN data {'sid': 'EXIp1SjjbwsxFy6ZAAAC', 'upgrades': ['websocket'], 'pingTimeout': 120000, 'pingInterval': 25000, 'maxPayload': 10000000.0}
Successfully emitted messages_marked_read event for user 3
Created new connection for thread 2027338422592. Active: 3
Released connection for thread 2027338427008 back to pool
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027337826112
Created new connection for thread 2027337826112. Active: 3
Created new connection for thread 2027337892928. Active: 4
Created new connection for thread 2027338426944. Active: 5
2025-05-19 23:19:07,616 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet OPEN data {'sid': 'EXIp1SjjbwsxFy6ZAAAC', 'upgrades': ['websocket'], 'pingTimeout': 120000, 'pingInterval': 25000, 'maxPayload': 10000000.0}
127.0.0.1 - - [19/May/2025 23:19:07] "GET /socket.io/?user_id=3&user_type=genius&t=PRf891T&EIO=4&transport=polling HTTP/1.1" 200 304 0.000999
EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 0
2025-05-19 23:19:07,625 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 0
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 0{"sid":"NSnYWNFTJsZ5tIq7AAAD"}
2025-05-19 23:19:07,626 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 0{"sid":"NSnYWNFTJsZ5tIq7AAAD"}
127.0.0.1 - - [19/May/2025 23:19:07] "POST /socket.io/?user_id=3&user_type=genius&t=PRf891d&EIO=4&transport=polling&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 219 0.001001
127.0.0.1 - - [19/May/2025 23:19:07] "GET /socket.io/?user_id=3&user_type=genius&t=PRf891f&EIO=4&transport=polling&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 213 0.000000
EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["join"]
2025-05-19 23:19:07,636 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["join"]
received event "join" from NSnYWNFTJsZ5tIq7AAAD [/]
2025-05-19 23:19:07,636 - socketio.server - INFO - received event "join" from NSnYWNFTJsZ5tIq7AAAD [/]
127.0.0.1 - - [19/May/2025 23:19:07] "POST /socket.io/?user_id=3&user_type=genius&t=PRf891o&EIO=4&transport=polling&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 219 0.001000
NSnYWNFTJsZ5tIq7AAAD is entering room 3 [/]
2025-05-19 23:19:07,637 - socketio.server - INFO - NSnYWNFTJsZ5tIq7AAAD is entering room 3 [/]
NSnYWNFTJsZ5tIq7AAAD is entering room NSnYWNFTJsZ5tIq7AAAD [/]
User 3 (genius) joined room 3
2025-05-19 23:19:07,638 - socketio.server - INFO - NSnYWNFTJsZ5tIq7AAAD is entering room NSnYWNFTJsZ5tIq7AAAD [/]
emitting event "join_confirmation" to NSnYWNFTJsZ5tIq7AAAD [/]
User 3 joined room NSnYWNFTJsZ5tIq7AAAD
2025-05-19 23:19:07,638 - socketio.server - INFO - emitting event "join_confirmation" to NSnYWNFTJsZ5tIq7AAAD [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["join_confirmation",{"user_id":3,"user_type":"genius","room":"3","sid":"NSnYWNFTJsZ5tIq7AAAD"}]
2025-05-19 23:19:07,638 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["join_confirmation",{"user_id":3,"user_type":"genius","room":"3","sid":"NSnYWNFTJsZ5tIq7AAAD"}]
127.0.0.1 - - [19/May/2025 23:19:07] "GET /api/contacts HTTP/1.1" 200 663 0.001108
127.0.0.1 - - [19/May/2025 23:19:07] "GET /socket.io/?user_id=3&user_type=genius&t=PRf891l&EIO=4&transport=polling&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 279 0.000893
(2084) accepted ('127.0.0.1', 57217)
EXIp1SjjbwsxFy6ZAAAC: Received request to upgrade to websocket
2025-05-19 23:19:07,930 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received request to upgrade to websocket
127.0.0.1 - - [19/May/2025 23:19:07] "GET /socket.io/?user_id=3&user_type=genius&t=PRf891_&EIO=4&transport=polling&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 181 0.280023
EXIp1SjjbwsxFy6ZAAAC: Upgrade to websocket successful
2025-05-19 23:19:07,934 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Upgrade to websocket successful
127.0.0.1 - - [19/May/2025 23:19:08] "GET /api/profile-photo/genius/3 HTTP/1.1" 200 2693174 2.607930
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\hubs\selects.py", line 59, in wait
    listeners.get(fileno, hub.noop).cb(fileno)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenthread.py", line 272, in main
    result = function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 905, in process_request
    self.protocol(conn_state, self)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 365, in __init__
    self.finish()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 829, in finish
    greenio.shutdown_safe(self.connection)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenio\base.py", line 477, in shutdown_safe
    return sock.shutdown(socket.SHUT_RDWR)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
Removing descriptor: 896
EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["mark_messages_read",{"contact_id":2}]
2025-05-19 23:19:08,889 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["mark_messages_read",{"contact_id":2}]
received event "mark_messages_read" from NSnYWNFTJsZ5tIq7AAAD [/]
2025-05-19 23:19:08,889 - socketio.server - INFO - received event "mark_messages_read" from NSnYWNFTJsZ5tIq7AAAD [/]
127.0.0.1 - - [19/May/2025 23:19:09] "GET /api/profile-photo/client/2 HTTP/1.1" 200 1895179 1.576656
emitting event "messages_marked_read" to 3 [/]
Marking messages as read from 2 to 3
Created new connection for thread 2027338189440. Active: 6
Messages table exists: True
Total messages in database: 122
Created new connection for thread 2027338223808. Active: 7
Messages between users 3 and 2: 122
Marked 0 messages as read
2025-05-19 23:19:10,408 - socketio.server - INFO - emitting event "messages_marked_read" to 3 [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
2025-05-19 23:19:10,409 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
emitting event "messages_marked_read" to NSnYWNFTJsZ5tIq7AAAD [/]
2025-05-19 23:19:10,410 - socketio.server - INFO - emitting event "messages_marked_read" to NSnYWNFTJsZ5tIq7AAAD [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
2025-05-19 23:19:10,410 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["messages_marked_read",{"contact_id":2,"messages_read":0,"unread_count":0,"messages":[{"id":794,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:10:39.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":793,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:08:49.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":792,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:32.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":791,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T15:07:05.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":790,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"hi","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:05:16.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":789,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"ty","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:43:41.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null},{"id":788,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:43:18.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":787,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a file: 05-19-2025.docx","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:40:03.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"05-19-2025.docx","file_mime_type":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","file_url":null},{"id":786,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"Sent a photo","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"file","timestamp":"2025-05-19T14:39:20.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":"Mens Haircuts Straight Hair.jpg","file_mime_type":"image/jpeg","file_url":null},{"id":785,"sender_id":2,"sender_type":"client","receiver_id":3,"receiver_type":"genius","message":"aw","is_read":1,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T14:38:56.000Z","status":"sent","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_name":null,"file_mime_type":null,"file_url":null}]}]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 641, in handle_one_response
    write(b''.join(towrite))
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 575, in write
    wfile.flush()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 724, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenio\base.py", line 383, in send
    return self._send_loop(self.fd.send, data, flags)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenio\base.py", line 370, in _send_loop
    return send_method(data, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 10053] An established connection was aborted by the software in your host machine

127.0.0.1 - - [19/May/2025 23:19:10] "GET /api/profile-photo/genius/3 HTTP/1.1" 200 82453 2.467469
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\hubs\selects.py", line 59, in wait
    listeners.get(fileno, hub.noop).cb(fileno)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenthread.py", line 272, in main
    result = function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 905, in process_request
    self.protocol(conn_state, self)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 365, in __init__
    self.finish()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\wsgi.py", line 824, in finish
    BaseHTTPServer.BaseHTTPRequestHandler.finish(self)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socketserver.py", line 819, in finish
    self.wfile.close()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 724, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenio\base.py", line 383, in send
    return self._send_loop(self.fd.send, data, flags)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\eventlet\greenio\base.py", line 370, in _send_loop
    return send_method(data, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 10053] An established connection was aborted by the software in your host machine
Removing descriptor: 1008
127.0.0.1 - - [19/May/2025 23:19:10] "GET /api/messages/2?limit=10&page=1 HTTP/1.1" 200 5506 2.971634
(2084) accepted ('127.0.0.1', 57223)
127.0.0.1 - - [19/May/2025 23:19:11] "GET /api/files/786?t=1747667950642 HTTP/1.1" 200 26961 1.219832
127.0.0.1 - - [19/May/2025 23:19:12] "GET /api/profile-photo/genius/3 HTTP/1.1" 200 2693174 1.868609
127.0.0.1 - - [19/May/2025 23:19:12] "GET /api/files/792?t=1747667950643 HTTP/1.1" 200 26961 2.135369
EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["send_message",{"receiver_id":2,"message":"dog","related_to_job_id":13,"related_to_application_id":8}]
Successfully emitted messages_marked_read event for user 3
Released connection for thread 2027338223808 back to pool
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338189440
Created new connection for thread 2027338189440. Active: 7
Created new connection for thread 2027338201792. Active: 8
2025-05-19 23:19:12,920 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet MESSAGE data 2["send_message",{"receiver_id":2,"message":"dog","related_to_job_id":13,"related_to_application_id":8}]
received event "send_message" from NSnYWNFTJsZ5tIq7AAAD [/]
2025-05-19 23:19:12,920 - socketio.server - INFO - received event "send_message" from NSnYWNFTJsZ5tIq7AAAD [/]
127.0.0.1 - - [19/May/2025 23:19:14] "GET /api/messages/batch/reactions?ids=785,786,787,788,789,790,791,792,793,794 HTTP/1.1" 200 683 3.096442
emitting event "receive_message" to 2 [/]
Received message from user 3 to user 2
Socket SID: NSnYWNFTJsZ5tIq7AAAD
Message data: {'receiver_id': 2, 'message': 'dog', 'related_to_job_id': 13, 'related_to_application_id': 8}
Created new connection for thread 2027338632704. Active: 9
Created new connection for thread 2027338418880. Active: 10
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338201792
2025-05-19 23:19:15,767 - socketio.server - INFO - emitting event "receive_message" to 2 [/]
emitting event "receive_message" to 3 [/]
2025-05-19 23:19:15,767 - socketio.server - INFO - emitting event "receive_message" to 3 [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["receive_message",{"id":795,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"dog","is_read":0,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:19:17.000Z","status":"sending","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_data":null,"file_name":null,"file_mime_type":null,"file_url":null}]
2025-05-19 23:19:15,768 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["receive_message",{"id":795,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"dog","is_read":0,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:19:17.000Z","status":"sending","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_data":null,"file_name":null,"file_mime_type":null,"file_url":null}]
emitting event "receive_message" to NSnYWNFTJsZ5tIq7AAAD [/]
2025-05-19 23:19:15,768 - socketio.server - INFO - emitting event "receive_message" to NSnYWNFTJsZ5tIq7AAAD [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["receive_message",{"id":795,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"dog","is_read":0,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:19:17.000Z","status":"sending","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_data":null,"file_name":null,"file_mime_type":null,"file_url":null}]
2025-05-19 23:19:15,768 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["receive_message",{"id":795,"sender_id":3,"sender_type":"genius","receiver_id":2,"receiver_type":"client","message":"dog","is_read":0,"is_auto":0,"related_to_job_id":13,"related_to_application_id":8,"message_type":"text","timestamp":"2025-05-19T15:19:17.000Z","status":"sending","is_deleted":0,"deleted_by_sender":0,"deleted_by_receiver":0,"reply_to_id":null,"replied_message_text":null,"file_data":null,"file_name":null,"file_mime_type":null,"file_url":null}]
(2084) accepted ('127.0.0.1', 57234)
(2084) accepted ('127.0.0.1', 57235)
127.0.0.1 - - [19/May/2025 23:19:16] "GET /api/files/786?t=1747667952915 HTTP/1.1" 200 26961 3.807647
127.0.0.1 - - [19/May/2025 23:19:17] "GET /api/files/792?t=1747667952916 HTTP/1.1" 200 26961 4.179429
127.0.0.1 - - [19/May/2025 23:19:17] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 672 0.001000
127.0.0.1 - - [19/May/2025 23:19:18] "GET /api/files/786?t=1747667955771 HTTP/1.1" 200 26961 2.872969
127.0.0.1 - - [19/May/2025 23:19:19] "GET /api/files/792?t=1747667955772 HTTP/1.1" 200 26961 4.152146
127.0.0.1 - - [19/May/2025 23:19:20] "GET /api/messages/batch/reactions?ids=795 HTTP/1.1" 200 481 4.389399
127.0.0.1 - - [19/May/2025 23:19:22] "GET /api/files/786?t=1747667955776 HTTP/1.1" 200 26961 5.941446
127.0.0.1 - - [19/May/2025 23:19:23] "GET /api/files/792?t=1747667955777 HTTP/1.1" 200 26961 6.645463
emitting event "message_status_update" to 3 [/]
Created new connection for thread 2027338201792. Active: 10
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027337892928
Created new connection for thread 2027337892928. Active: 10
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338632704
Created new connection for thread 2027338632704. Active: 10
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338189440
Created new connection for thread 2027338189440. Active: 10
Created new connection for thread 2027338807104. Active: 11
Created new connection for thread 2027338806464. Active: 12
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338201792
Created new connection for thread 2027338201792. Active: 12
Created new connection for thread 2027337921600. Active: 13
2025-05-19 23:19:23,960 - socketio.server - INFO - emitting event "message_status_update" to 3 [/]
EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["message_status_update",{"message_id":795,"status":"sent"}]
2025-05-19 23:19:23,961 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet MESSAGE data 2["message_status_update",{"message_id":795,"status":"sent"}]
127.0.0.1 - - [19/May/2025 23:19:25] "GET /api/files/786?t=1747667963964 HTTP/1.1" 200 26961 1.505364
127.0.0.1 - - [19/May/2025 23:19:26] "GET /api/files/792?t=1747667963967 HTTP/1.1" 200 26961 2.139163
EXIp1SjjbwsxFy6ZAAAC: Sending packet PING data None
Error updating message status: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338201792
Created new connection for thread 2027338201792. Active: 13
Connection is no longer alive: MySQL Connection not available
Closed stale connection for thread 2027338806464
Created new connection for thread 2027338806464. Active: 13
2025-05-19 23:19:32,617 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet PING data None
EXIp1SjjbwsxFy6ZAAAC: Received packet PONG data 
2025-05-19 23:19:32,618 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet PONG data 
EXIp1SjjbwsxFy6ZAAAC: Sending packet PING data None
2025-05-19 23:19:57,621 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Sending packet PING data None
EXIp1SjjbwsxFy6ZAAAC: Received packet PONG data 
2025-05-19 23:19:57,622 - engineio.server - INFO - EXIp1SjjbwsxFy6ZAAAC: Received packet PONG data 
127.0.0.1 - - [19/May/2025 23:20:01] "GET /socket.io/?user_id=3&user_type=genius&t=1747667947611&EIO=4&transport=websocket&sid=EXIp1SjjbwsxFy6ZAAAC HTTP/1.1" 200 0 53.759925
