:root {
    /* Primary colors */
    --primary-blue: #004AAD;
    --primary-pink: #CD208B;
    --primary-light-blue: #E6F0FF;
    --primary-light-pink: #FFF0F8;

    /* Neutral colors */
    --neutral-100: #FFFFFF;
    --neutral-200: #F8FAFC;
    --neutral-300: #EEF2F6;
    --neutral-400: #E2E8F0;
    --neutral-500: #CBD5E1;
    --neutral-600: #94A3B8;
    --neutral-700: #64748B;
    --neutral-800: #334155;
    --neutral-900: #1E293B;

    /* Accent colors */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;

    /* Typography */
    --font-family: 'Poppins', system-ui, -apple-system, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-blue: 0 8px 16px rgba(0, 74, 173, 0.15);
    --shadow-pink: 0 8px 16px rgba(205, 32, 139, 0.15);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--neutral-200);
    color: var(--neutral-900);
    line-height: 1.6;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 20px auto 0;
    padding: var(--spacing-8);
    background-color: var(--neutral-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    min-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Title section */
.title-section {
    margin: var(--spacing-12) 0 var(--spacing-12);
    text-align: center;
}

.title-section h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: var(--spacing-4);
    line-height: 1.2;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.title-section p {
    font-size: var(--font-size-lg);
    color: var(--neutral-700);
    max-width: 700px;
    margin: 0 auto;
}

/* Dropdown section */
.dropdown-section {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    padding: var(--spacing-8);
    background-color: var(--neutral-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-300);
}

/* Job post dropdown */
.job-post-dropdown {
    padding: var(--spacing-6);
    border-radius: var(--border-radius-md);
    background-color: var(--neutral-100);
    border: 1px solid var(--neutral-400);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-6);
}

.job-post-dropdown:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-blue);
    transform: translateY(-2px);
}

.job-post-dropdown.active {
    border-color: var(--primary-blue);
    background-color: var(--primary-light-blue);
}

.job-post-text {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--neutral-800);
}

.job-post-dropdown i {
    color: var(--primary-blue);
    font-size: var(--font-size-xl);
    transition: var(--transition-normal);
}

.job-post-dropdown.active i {
    transform: rotate(180deg);
}

/* Dropdown options */
.dropdown-options {
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.dropdown-options.show {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
    opacity: 1;
    transform: translateY(0);
    margin-bottom: var(--spacing-8);
}

/* Option box */
.option-box {
    padding: var(--spacing-8);
    border-radius: var(--border-radius-lg);
    background-color: var(--neutral-100);
    border: 1px solid var(--neutral-400);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.option-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    opacity: 0;
    transition: var(--transition-normal);
}

.option-box:hover::before,
.option-box.selected::before {
    opacity: 1;
}

.option-box:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--neutral-500);
}

.option-box.selected {
    background-color: var(--primary-light-blue);
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-blue);
}

.option-box .icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-4);
    display: block;
    color: var(--primary-blue);
}

.option-box h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-2);
}

.option-box p {
    font-size: var(--font-size-md);
    color: var(--neutral-700);
    margin-bottom: var(--spacing-4);
}

.option-box .details {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    line-height: 1.8;
}

.option-box .details i {
    color: var(--primary-blue);
    margin-right: var(--spacing-2);
}

/* Draft container */
.draft-container {
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--neutral-300);
}

/* Simple dropdown box */
.simple-dropdown-box {
    display: none;
    position: absolute;
    background-color: var(--neutral-100);
    border: 1px solid var(--neutral-400);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    width: 300px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
}

.simple-dropdown-box.show {
    display: block;
    animation: slideDown 0.3s ease forwards;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Draft option */
.draft-option {
    padding: var(--spacing-4) var(--spacing-6);
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--neutral-300);
    font-size: var(--font-size-md);
    color: var(--neutral-800);
}

.draft-option:last-child {
    border-bottom: none;
}

.draft-option:hover {
    background-color: var(--primary-light-blue);
    color: var(--primary-blue);
}

.draft-option.selected {
    background-color: var(--primary-light-blue);
    color: var(--primary-blue);
    font-weight: 500;
}

.draft-option.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-blue);
    float: right;
}

/* Custom scrollbar */
.simple-dropdown-box::-webkit-scrollbar {
    width: 6px;
}

.simple-dropdown-box::-webkit-scrollbar-track {
    background: var(--neutral-200);
    border-radius: var(--border-radius-full);
}

.simple-dropdown-box::-webkit-scrollbar-thumb {
    background: var(--neutral-500);
    border-radius: var(--border-radius-full);
}

.simple-dropdown-box::-webkit-scrollbar-thumb:hover {
    background: var(--neutral-600);
}

/* Button container */
.button-container {
    margin-top: auto;
    padding: var(--spacing-6) 0;
    background-color: var(--neutral-100);
    border-top: 1px solid var(--neutral-300);
    position: sticky;
    bottom: 0;
}

.buttons-wrapper {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-4);
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

/* Buttons */
.continue-button,
.cancel-button {
    padding: var(--spacing-4) var(--spacing-8);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    transition: var(--transition-normal);
    cursor: pointer;
    border: none;
    outline: none;
}

.continue-button {
    background-color: var(--primary-blue);
    color: var(--neutral-100);
    box-shadow: var(--shadow-blue);
}

.continue-button:not(:disabled):hover {
    background-color: #003d8f;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 74, 173, 0.2);
}

.continue-button:disabled {
    background-color: var(--neutral-400);
    color: var(--neutral-600);
    cursor: not-allowed;
    box-shadow: none;
}

.cancel-button {
    background-color: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-400);
}

.cancel-button:hover {
    background-color: var(--neutral-200);
    color: var(--neutral-900);
    border-color: var(--neutral-500);
}

/* Alert styles */
.alert {
    padding: var(--spacing-4) var(--spacing-6);
    margin-bottom: var(--spacing-4);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

/* Decorative elements */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 10%, rgba(0, 74, 173, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(205, 32, 139, 0.03) 0%, transparent 30%);
    pointer-events: none;
    z-index: -1;
    border-radius: var(--border-radius-lg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body {
        padding: 0;
        margin: 0;
        background-color: var(--neutral-200);
    }

    .container {
        padding: var(--spacing-4);
        margin: 10px;
        border-radius: var(--border-radius-md);
        min-height: calc(100vh - 20px);
    }

    .title-section {
        margin: var(--spacing-6) 0 var(--spacing-8);
        padding: 0 var(--spacing-2);
    }

    .title-section h1 {
        font-size: var(--font-size-3xl);
        line-height: 1.3;
        margin-bottom: var(--spacing-3);
    }

    .title-section p {
        font-size: var(--font-size-md);
        padding: 0 var(--spacing-2);
    }

    .dropdown-section {
        padding: var(--spacing-4);
        margin: 0;
        box-shadow: none;
        border: none;
        background-color: transparent;
    }

    .dropdown-options.show {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
        margin-bottom: var(--spacing-6);
    }

    .option-box {
        padding: var(--spacing-6);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-sm);
    }

    .option-box .icon {
        font-size: 1.5rem;
        margin-bottom: var(--spacing-3);
    }

    .option-box h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-2);
    }

    .option-box p {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-3);
    }

    .option-box .details {
        font-size: var(--font-size-xs);
    }

    .job-post-dropdown {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
        border-radius: var(--border-radius-md);
    }

    .job-post-text {
        font-size: var(--font-size-md);
    }

    .draft-container {
        margin-top: var(--spacing-6);
        padding-top: var(--spacing-6);
    }

    .simple-dropdown-box {
        width: 100%;
        max-width: none;
        left: 0 !important;
        right: 0;
        margin: 0 var(--spacing-2);
        width: calc(100% - var(--spacing-4));
    }

    .button-container {
        padding: var(--spacing-4) 0;
        margin-top: var(--spacing-6);
        position: sticky;
        bottom: 0;
        background-color: var(--neutral-100);
        border-top: 1px solid var(--neutral-300);
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }

    .buttons-wrapper {
        flex-direction: row;
        gap: var(--spacing-3);
        padding: 0 var(--spacing-4);
    }

    .continue-button,
    .cancel-button {
        flex: 1;
        padding: var(--spacing-4);
        font-size: var(--font-size-md);
        border-radius: var(--border-radius-md);
    }

    /* Progress indicator mobile adjustments */
    .progress-indicator {
        margin-bottom: var(--spacing-6);
        padding: 0 var(--spacing-2);
    }

    .progress-step {
        margin: 0 var(--spacing-2);
    }

    .step-number {
        width: 2rem;
        height: 2rem;
        font-size: var(--font-size-md);
        margin-right: var(--spacing-1);
    }

    .step-label {
        font-size: var(--font-size-xs);
        display: none; /* Hide labels on very small screens */
    }

    .step-connector {
        min-width: 1rem;
        max-width: 2rem;
        margin: 0 var(--spacing-1);
    }
}

@media (max-width: 480px) {
    .container {
        margin: 5px;
        padding: var(--spacing-3);
        border-radius: var(--border-radius-sm);
    }

    .title-section {
        margin: var(--spacing-4) 0 var(--spacing-6);
    }

    .title-section h1 {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }

    .title-section p {
        font-size: var(--font-size-sm);
    }

    .dropdown-section {
        padding: var(--spacing-3);
    }

    .option-box {
        padding: var(--spacing-4);
    }

    .option-box .icon {
        font-size: 1.25rem;
    }

    .option-box h3 {
        font-size: var(--font-size-md);
    }

    .option-box p {
        font-size: var(--font-size-xs);
    }

    .job-post-dropdown {
        padding: var(--spacing-3);
    }

    .job-post-text {
        font-size: var(--font-size-sm);
    }

    .buttons-wrapper {
        flex-direction: column-reverse;
        gap: var(--spacing-2);
    }

    .continue-button,
    .cancel-button {
        width: 100%;
        padding: var(--spacing-3);
    }

    /* Show step labels on very small screens in a compact way */
    .step-label {
        display: block;
        font-size: 0.6rem;
        line-height: 1;
    }

    .progress-step {
        flex-direction: column;
        align-items: center;
        margin: 0 var(--spacing-1);
    }

    .step-number {
        margin-right: 0;
        margin-bottom: var(--spacing-1);
        width: 1.75rem;
        height: 1.75rem;
        font-size: var(--font-size-sm);
    }
}

/* Progress indicator */
.progress-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-8);
}

.progress-step {
    display: flex;
    align-items: center;
    margin: 0 var(--spacing-4);
}

.step-number {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    background-color: var(--neutral-300);
    color: var(--neutral-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-2);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.step-number.active {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    color: var(--neutral-100);
    box-shadow: var(--shadow-blue);
}

.step-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    font-weight: 500;
}

.step-label.active {
    color: var(--primary-blue);
    font-weight: 600;
}

.step-connector {
    flex: 1;
    height: 2px;
    background-color: var(--neutral-300);
    margin: 0 var(--spacing-2);
    min-width: 2rem;
    max-width: 4rem;
}

.step-connector.active {
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
}

/* Special positioning for the nested dropdown */
#dropdownOptions5 {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    max-width: 300px;
    z-index: 1010;
}

/* Animation for the nested dropdown */
@keyframes slideFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Mobile-specific touch improvements */
@media (max-width: 768px) {
    /* Improve touch targets */
    .job-post-dropdown,
    .option-box,
    .draft-option,
    .continue-button,
    .cancel-button {
        min-height: 44px; /* iOS recommended touch target size */
        -webkit-tap-highlight-color: transparent;
    }

    /* Better touch feedback */
    .option-box:active {
        transform: translateY(-2px) scale(0.98);
        transition: transform 0.1s ease;
    }

    .job-post-dropdown:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .continue-button:active,
    .cancel-button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }

    /* Smooth scrolling for mobile */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Better dropdown positioning on mobile */
    .simple-dropdown-box {
        position: fixed !important;
        left: var(--spacing-2) !important;
        right: var(--spacing-2) !important;
        width: auto !important;
        max-height: 60vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile-friendly scrollbar */
    .simple-dropdown-box::-webkit-scrollbar {
        width: 8px;
    }

    .simple-dropdown-box::-webkit-scrollbar-thumb {
        background: var(--neutral-400);
        border-radius: var(--border-radius-full);
    }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
    .container {
        margin: 2px;
        padding: var(--spacing-2);
    }

    .title-section h1 {
        font-size: var(--font-size-xl);
    }

    .option-box {
        padding: var(--spacing-3);
    }

    .option-box h3 {
        font-size: var(--font-size-sm);
    }

    .job-post-dropdown {
        padding: var(--spacing-2);
    }

    .buttons-wrapper {
        padding: 0 var(--spacing-2);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .container {
        margin: 5px;
        min-height: calc(100vh - 10px);
    }

    .title-section {
        margin: var(--spacing-4) 0 var(--spacing-6);
    }

    .title-section h1 {
        font-size: var(--font-size-2xl);
    }

    .progress-indicator {
        margin-bottom: var(--spacing-4);
    }

    .dropdown-section {
        padding: var(--spacing-3);
    }

    .button-container {
        padding: var(--spacing-3) 0;
    }
}




