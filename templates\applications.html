<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Applications - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--dark-color);
            background-color: #f9fafb;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            padding-top: 80px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: var(--light-color);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .sidebar-menu i {
            margin-right: 10px;
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 250px;
            padding: 80px 20px 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .application-card {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .application-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        .application-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .application-body {
            padding: 20px;
        }

        .application-footer {
            padding: 15px 20px;
            border-top: 1px solid #e5e7eb;
            background-color: #f9fafb;
            border-radius: 0 0 12px 12px;
        }

        .applicant-info {
            display: flex;
            align-items: center;
        }

        .applicant-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
        }

        .applicant-name {
            font-weight: 600;
            margin-bottom: 0;
        }

        .applicant-position {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }

        .status-accepted {
            background-color: #d1fae5;
            color: #059669;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .job-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .job-description {
            color: #6b7280;
            margin-bottom: 15px;
        }

        .application-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .meta-item i {
            margin-right: 5px;
            width: 16px;
            text-align: center;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
        }

        .empty-state i {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #6b7280;
            max-width: 500px;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand ms-4" href="#">
                <i class="fas fa-bolt me-2"></i>
                GigGenius
            </a>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown">
                        <img src="https://ui-avatars.com/api/?name={{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}&background=2563eb&color=fff" alt="User" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                        <span>{{ session.get('first_name', '') }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> My Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <ul class="sidebar-menu">
            {% if session.get('user_type') == 'genius' %}
            <li>
                <a href="{{ url_for('genius_page') }}">
                    <i class="fas fa-home"></i> Dashboard
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-briefcase"></i> Jobs
                </a>
            </li>
            <li>
                <a href="{{ url_for('applications') }}" class="active">
                    <i class="fas fa-paper-plane"></i> Applications
                </a>
            </li>
            {% else %}
            <li>
                <a href="{{ url_for('client_page') }}">
                    <i class="fas fa-home"></i> Dashboard
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-briefcase"></i> My Jobs
                </a>
            </li>
            <li>
                <a href="{{ url_for('applications') }}" class="active">
                    <i class="fas fa-paper-plane"></i> Applications
                </a>
            </li>
            {% endif %}
            <li>
                <a href="{{ url_for('chat') }}">
                    <i class="fas fa-comments"></i> Messages
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-user"></i> Profile
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    {% if session.get('user_type') == 'genius' %}
                    My Applications
                    {% else %}
                    Job Applications
                    {% endif %}
                </h1>
                {% if session.get('user_type') == 'client' %}
                <a href="{{ url_for('submit_job') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Post a Job
                </a>
                {% endif %}
            </div>

            {% if applications %}
                {% for app in applications %}
                <div class="application-card">
                    <div class="application-header">
                        <div class="applicant-info">
                            {% if app.profile_photo %}
                            <img src="{{ app.profile_photo }}" alt="Applicant" class="applicant-avatar">
                            {% else %}
                            <img src="https://ui-avatars.com/api/?name={{ app.first_name[0] }}{{ app.last_name[0] }}&background=2563eb&color=fff" alt="Applicant" class="applicant-avatar">
                            {% endif %}
                            <div>
                                <p class="applicant-name">{{ app.first_name }} {{ app.last_name }}</p>
                                {% if app.position %}
                                <p class="applicant-position">{{ app.position }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <span class="status-badge status-{{ app.status }}">{{ app.status|capitalize }}</span>
                    </div>
                    <div class="application-body">
                        <h5 class="job-title">{{ app.job_title }}</h5>
                        <p class="job-description">{{ app.job_description_short }}</p>
                        <div class="application-meta">
                            <div class="meta-item">
                                <i class="far fa-calendar-alt"></i> Applied: {{ app.created_at }}
                            </div>
                            {% if app.updated_at and app.updated_at != app.created_at %}
                            <div class="meta-item">
                                <i class="fas fa-clock"></i> Updated: {{ app.updated_at }}
                            </div>
                            {% endif %}
                            {% if session.get('user_type') == 'genius' and app.business_name %}
                            <div class="meta-item">
                                <i class="fas fa-building"></i> {{ app.business_name }}
                            </div>
                            {% endif %}
                        </div>
                        {% if session.get('user_type') == 'client' %}
                        <div class="d-flex gap-2">
                            {% if app.status == 'pending' %}
                            <button class="btn btn-sm btn-success" onclick="updateApplicationStatus({{ app.id }}, 'accepted')">
                                <i class="fas fa-check me-1"></i> Accept
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="updateApplicationStatus({{ app.id }}, 'rejected')">
                                <i class="fas fa-times me-1"></i> Reject
                            </button>
                            {% elif app.status == 'accepted' %}
                            <button class="btn btn-sm btn-outline-primary" onclick="contactApplicant({{ app.id }})">
                                <i class="fas fa-envelope me-1"></i> Contact
                            </button>
                            {% endif %}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteApplication({{ app.id }})">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                        </div>
                        {% else %}
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteApplication({{ app.id }})">
                                <i class="fas fa-trash me-1"></i> Delete Application
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="card">
                    <div class="card-body">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h3>No applications found</h3>
                            {% if session.get('user_type') == 'genius' %}
                            <p>You haven't applied to any jobs yet. Browse available jobs and submit your first application!</p>
                            <a href="{{ url_for('genius_page') }}" class="btn btn-primary">Browse Jobs</a>
                            {% else %}
                            <p>You don't have any applications for your jobs yet. Post more jobs to attract talented applicants!</p>
                            <a href="{{ url_for('submit_job') }}" class="btn btn-primary">Post a Job</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteApplication(applicationId) {
            if (confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
                fetch('/delete_application', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        application_id: applicationId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Create and show a success toast
                        const successToast = document.createElement('div');
                        successToast.className = 'position-fixed bottom-0 end-0 p-3';
                        successToast.style.zIndex = '5';
                        successToast.innerHTML = `
                            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-success text-white">
                                    <strong class="me-auto">Success</strong>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    Application deleted successfully!
                                </div>
                            </div>
                        `;
                        document.body.appendChild(successToast);

                        // Remove toast after 3 seconds
                        setTimeout(() => {
                            successToast.remove();
                        }, 3000);

                        // Remove the application card from the UI
                        const applicationCard = document.querySelector(`button[onclick="deleteApplication(${applicationId})"]`).closest('.application-card');
                        applicationCard.style.opacity = '0';
                        applicationCard.style.transform = 'translateY(-10px)';
                        applicationCard.style.transition = 'all 0.3s ease';

                        setTimeout(() => {
                            applicationCard.remove();

                            // Check if there are no more applications
                            const remainingApplications = document.querySelectorAll('.application-card');
                            if (remainingApplications.length === 0) {
                                // Show empty state
                                const mainContent = document.querySelector('.main-content .container-fluid');
                                const emptyState = `
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="empty-state">
                                                <i class="fas fa-inbox"></i>
                                                <h3>No applications found</h3>
                                                {% if session.get('user_type') == 'genius' %}
                                                <p>You haven't applied to any jobs yet. Browse available jobs and submit your first application!</p>
                                                <a href="{{ url_for('genius_page') }}" class="btn btn-primary">Browse Jobs</a>
                                                {% else %}
                                                <p>You don't have any applications for your jobs yet. Post more jobs to attract talented applicants!</p>
                                                <a href="{{ url_for('submit_job') }}" class="btn btn-primary">Post a Job</a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                `;
                                mainContent.innerHTML = `
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <h1 class="h3 mb-0">
                                            {% if session.get('user_type') == 'genius' %}
                                            My Applications
                                            {% else %}
                                            Job Applications
                                            {% endif %}
                                        </h1>
                                        {% if session.get('user_type') == 'client' %}
                                        <a href="{{ url_for('submit_job') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i> Post a Job
                                        </a>
                                        {% endif %}
                                    </div>
                                    ${emptyState}
                                `;
                            }
                        }, 300);
                    } else {
                        // Show error message
                        alert('Error: ' + (data.error || 'An error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        }

        {% if session.get('user_type') == 'client' %}
        function updateApplicationStatus(applicationId, status) {
            if (confirm(`Are you sure you want to ${status} this application?`)) {
                fetch('/update_application_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        application_id: applicationId,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show updated status
                        window.location.reload();
                    } else {
                        alert('Error: ' + (data.error || 'An error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        }

        function contactApplicant(applicationId) {
            alert('This feature is not yet implemented. In a real application, this would open a messaging interface.');
        }
        {% endif %}
    </script>
</body>
</html>
