"""
Socket.IO Connection Test Script

This script tests the Socket.IO connection to the server.
Run this script to verify that Socket.IO is working correctly.
"""

import socketio
import time
import requests
import json
import logging
import sys
import os

# Force output to be unbuffered
os.environ['PYTHONUNBUFFERED'] = '1'

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)

print("Starting Socket.IO test script...")
print("Python version:", sys.version)
try:
    print("Socket.IO client version:", socketio.__version__)
except AttributeError:
    print("Socket.IO client version: Unknown")

# Create a Socket.IO client
print("Creating Socket.IO client...")
sio = socketio.Client(logger=True, engineio_logger=True)

# Define event handlers
@sio.event
def connect():
    print("Connected to Socket.IO server!")
    print("Socket ID:", sio.sid)

@sio.event
def connect_error(data):
    print("Connection error:", data)

@sio.event
def disconnect():
    print("Disconnected from Socket.IO server")

# First check if server is running
print("Checking if server is running...")
try:
    # Try a simple request first
    response = requests.get('http://127.0.0.1:8000/')
    print(f"Basic server check: Status code {response.status_code}")

    # Now try the debug endpoint
    response = requests.get('http://127.0.0.1:8000/socket-debug')
    print("Socket debug endpoint status:", response.status_code)
    if response.status_code == 200:
        print(json.dumps(response.json(), indent=2))
    else:
        print("Response content:", response.text[:200])  # Print first 200 chars
except Exception as e:
    print("Error checking server status:", e)
    print("Make sure your Flask server is running on port 8000")
    sys.exit(1)

# Connect to the Socket.IO server
print("Attempting to connect to Socket.IO server...")
try:
    # Try with polling transport only first
    print("Connecting with polling transport only...")
    sio.connect(
        'http://127.0.0.1:8000',
        transports=['polling'],
        wait_timeout=10,
        socketio_path='socket.io'
    )

    # Wait for a few seconds to see if we receive any events
    print("Waiting for events...")
    time.sleep(5)

    # Disconnect
    print("Disconnecting...")
    sio.disconnect()
except Exception as e:
    print("Error connecting to Socket.IO server:", e)
    import traceback
    traceback.print_exc()

print("Test script completed.")
