<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PH Health Insurance</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
        }

        html {
            font-size: 16px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            line-height: 1.6;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 5%;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000; /* Ensure navbar stays on top */
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--primary-pink);
            gap: 1rem;
        }

        .logo img {
            width: 50px;
            height: 50px;
        }

        .logo:hover {
            color: var(--primary-blue); 
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
            margin-left: 2rem;
        }

        .nav-links a {
            font-weight: 500;
            font-size: 1.2rem;  /* Bigger navigation links */
            color: var(--primary-blue);
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .nav-links a:hover {
            color: var(--primary-pink);
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-container {
            display: flex;
            height: 50px;  /* Taller search container */
            margin-right: 1rem;
        }

        .search-type-select {
            position: relative;
            height: 100%;
        }

        .search-type-button {
            height: 50px;  /* Taller search type button */
            background: white;
            border: 2px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1.2rem;  /* Bigger text */
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 0.8rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 55px;  /* Adjusted for taller search bar */
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            min-width: 120px;
            display: none;
            z-index: 1000;
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem 1.5rem;  /* Bigger padding */
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            background: #f5f5f5;
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem; 
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;  
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;  /* Adjusted padding */
            border-radius: 8px;  /* Match search bar border-radius */
            font-size: 1rem;    /* Standard font size */
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;   /* Reduced minimum width */
            height: 50px;       /* Match search bar height */
        }

        .navbar .btn {
            min-width: 120px;
            height: 50px;       /* Match search bar height */
            padding: 0 1.5rem;
            font-size: 1rem;
        }

        section .btn {
            min-width: 120px;
            height: 50px;       /* Match search bar height */
            padding: 0 1.5rem;
            font-size: 1rem;
        }

        .modal .btn {
            width: 100%;
            height: 50px;       /* Match search bar height */
            margin: 0.5rem 0;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: white;
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: white;
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        a {
            text-decoration: none;
        }

        .navbar .auth-buttons {
            gap: 0.8rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }
        
        .modal-content h2 {
            font-size: 24px; 
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem; /* Add padding on sides */
        }

        .modal-buttons .btn {
            width: auto; /* Remove fixed width */
            min-width: 160px; /* Set reasonable min-width */
            max-width: 80%; /* Prevent button from being too wide */
            margin: 0 auto; /* Center the button */
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: #f0f8ff;
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: #ffe5f0;
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        footer {
            background: var(--primary-blue);
            padding: 2rem 5% 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }
        .footer-column h3 {
            margin-bottom: 1rem;
            color: white;
            font-family: 'Arial', sans-serif;
        }

        .footer-column a {
            display: block;
            color: white;
            text-decoration: none;
            margin-bottom: 0.5rem;
            font-family: 'Arial', sans-serif;
            transition: text-decoration 0.3s ease; 
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: white;
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 100px;
        }

        .footer-bottom a {
            color: white;
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 24px;
            height: 24px;
            margin: 0 5px;
        }

        .social-icons .bi {
            font-size: 1.5rem; 
            margin-right: 10px;
            border-radius: 50%;
            color: white;
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .social-login-buttons {
            margin: 1.5rem 0;
            width: 100%;
            padding-right: 20px; /* Add some padding from the right edge */
        }

        .social-login-btn {
            width: 100%;
            height: 45px;
            border-radius: 15px;
            border: 1px solid #ddd;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .social-login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .social-login-btn:active {
            transform: translateY(-1px);
        }

        .social-login-btn i {
            font-size: 1.3rem;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .social-login-btn span {
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .social-login-btn .bi-google {
            color: #DB4437;
        }

        .social-login-btn:hover .bi {
            transform: scale(1.2);
        }

        .social-login-btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: scale(0);
            transition: transform 0.3s ease-out;
        }

        .social-login-btn:active::after {
            transform: scale(2);
            opacity: 0;
        }

        .social-login-btn {
            animation: slideInRight 0.5s ease forwards;
            opacity: 0;
        }

        .social-login-btn:nth-child(1) { animation-delay: 0.1s; }
        .social-login-btn:nth-child(2) { animation-delay: 0.2s; }
        .social-login-btn:nth-child(3) { animation-delay: 0.3s; }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .security-code-modal {
            max-width: 450px;
            padding: 2rem;
            text-align: center;
        }

        .security-code-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .email-display {
            background-color: #f5f5f5;
            padding: 0.8rem;
            margin: 1rem 0;
            border-radius: 5px;
            font-weight: 500;
            word-break: break-all;
        }

        .security-code-inputs {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin: 1.5rem 0;
        }

        .code-input {
            width: 40px;
            height: 50px;
            text-align: center;
            font-size: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .code-input:focus {
            border-color: var(--primary-blue);
            outline: none;
        }

        .resend-code {
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .resend-code a {
            color: var(--primary-blue);
            text-decoration: none;
        }

        .resend-code a:hover {
            text-decoration: underline;
        }
        #verificationModal .modal-content {
            max-width: 500px;
        }
        
        #verificationMessage {
            margin: 20px 0;
            font-size: 1.1rem;
            text-align: center;
            line-height: 1.5;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0;
            padding-top: 100px; /* Add padding to account for fixed navbar */
        }
        
        .introduction-section {
            width: 100%;
            margin-bottom: 40px;
            padding-top: 20px; /* Additional spacing from navbar */
        }

        .introduction-content {
            width: 100%; 
            height: 600px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin: 0 auto; 
            position: relative;
        }

        .slideshow {
            position: relative;
            width: 100%;
        }

        .slide {
            display: none;
            animation: fade 3s ease-in-out; /* Increased from 2s */
        }

        .slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Smaller arrows */
        .prev, .next {
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            padding: 12px; /* Reduced from 16px */
            color: white;
            font-weight: bold;
            font-size: 18px; /* Reduced from 24px */
            transition: 0.6s ease;
            border-radius: 50%;
            user-select: none;
            background: rgba(0, 0, 0, 0.3);
            width: 40px; /* Reduced from 50px */
            height: 40px; /* Reduced from 50px */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .next {
            right: 15px; /* Reduced from 20px */
        }

        .prev {
            left: 15px; /* Reduced from 20px */
        }

        .prev:hover, .next:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .fade {
            animation-name: fade;
            animation-duration: 3s; /* Increased from 1.5s */
        }

        @keyframes fade {
            from {opacity: .4} 
            to {opacity: 1}
        }
        
        .benefits-section {
            display: flex;
            align-items: center;
            gap: 3rem;
            padding: 3rem;
            max-width: 1500px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #eee;
        }

        .benefits-image {
            flex: 1;
            max-width: 500px;
        }

        .benefits-image img {
            width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .benefits-image img:hover {
            transform: scale(1.02);
        }

        .benefits-content {
            flex: 1;
            padding: 0;
            font-family: 'Poppins', sans-serif;
            text-align: justify;
            line-height: 1.6;
            font-size: 1.2rem;
        }

        .benefits-content h2 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: var(--primary-pink);
            font-family: 'Poppins', sans-serif;
            font-weight: bold; 
        }

        .benefits-content h2 span {
            color: var(--primary-blue);
            font-weight: bold; 
        }

        .benefits-content p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }

        .checklist {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }

        .checklist li {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            color: var(--text-dark);
        }

        .checklist li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .features-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .feature-card {
            width: 32%;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .kwikcare-logo {
            color: #0e76bc;
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .kwikcare-logo span {
            color: #30c4c4;
        }

        .kwikcare-social {
            margin-top: 1rem;
            text-align: center;
        }

        .kwikcare-image {
            width: 50%;
        }

        .kwikcare-image img {
            width: 100%;
            height: auto;
            display: block;
        }
        .social-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 0.5rem;
        }

        .social-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            background-color: #f5f5f5;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background-color: var(--primary-blue);
            color: white;
        }
    </style>
</head>
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <button onclick="continueToRegistration()" class="btn btn-primary">Continue</button>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeVerificationModal()">&times;</span>
            <h2>Confirm Your Selection</h2>
            <div id="verificationMessage"></div>
            <div class="modal-buttons">
                <button onclick="proceedToRegistration()" class="btn btn-primary">Proceed</button>
                <button onclick="closeVerificationModal()" class="btn btn-outline">Go Back</button>
            </div>
        </div>
    </div>

    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <button class="btn btn-outline" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp3.png') }}" alt="Google" style="width: 20px; height: 20px; margin-right: 10px;">
                    Continue with Google
                </button>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <button class="btn btn-primary" onclick="submitForgotPassword()">Submit</button>
        </div>
    </div>

    <div id="securityCodeModal" class="modal">
        <div class="modal-content security-code-modal">
            <span class="close" onclick="closeSecurityCodeModal()">&times;</span>
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 80px; height: 80px; border-radius: 50%;">
            </div>
            <h2>Verify Security Code</h2>
            <p>We'll send a security code to your email for verification.</p>
            <div class="email-display" id="securityCodeEmail"></div>
            <div class="code-input-container" style="display: none;" id="codeInputContainer">
                <p>Enter the 6-digit code sent to your email:</p>
                <div class="security-code-inputs">
                    <input type="text" maxlength="1" class="code-input" data-index="1">
                    <input type="text" maxlength="1" class="code-input" data-index="2">
                    <input type="text" maxlength="1" class="code-input" data-index="3">
                    <input type="text" maxlength="1" class="code-input" data-index="4">
                    <input type="text" maxlength="1" class="code-input" data-index="5">
                    <input type="text" maxlength="1" class="code-input" data-index="6">
                </div>
                <div id="codeErrorMessage" style="color: red; margin-top: 0.5rem; display: none;"></div>
                <button class="btn btn-primary" onclick="verifySecurityCode()">Verify</button>
                <p class="resend-code">
                    Didn't receive the code? <a href="javascript:void(0)" onclick="resendSecurityCode()">Resend Code</a>
                </p>
            </div>
            <button class="btn btn-primary" id="sendCodeBtn" onclick="sendSecurityCode()">Send Code</button>
        </div>
    </div>

<body>
    <nav class="navbar">
        <div style="display: flex; align-items: center;">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    GigGenius
                </div>
            </a>
            <div class="nav-links">
                <a href="{{ url_for('find_geniuses') }}">Find Geniuses</a>
                <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                <a href="{{ url_for('why_giggenius') }}">Why GigGenius</a>
            </div>
        </div>
        
        <div class="right-section">
            <div class="search-container">
                <div class="search-type-select">
                    <button class="search-type-button" id="searchTypeBtn">
                        <span id="selectedSearchType">All</span>
                    </button>
                    <div class="search-type-dropdown" id="searchTypeDropdown">
                        <div class="search-type-option" data-value="all">All</div>
                        <div class="search-type-option" data-value="genius">Genius</div>
                        <div class="search-type-option" data-value="gigs">Gigs</div>
                        <div class="search-type-option" data-value="client">Client</div>
                    </div>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search...">
                    <i class="fas fa-search icon"></i>
                </div>
            </div>
            <div class="auth-buttons">
                <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Introduction Section -->
        <div class="introduction-section">    
            <div class="introduction-content slideshow">
                <div class="slide fade">
                    <img src="/static/img/phhi3.jpg">
                </div>
                <div class="slide fade">
                    <img src="/static/img/phhi2.png">
                </div>
                <a class="prev" onclick="changeSlide(-1)">❮</a>
                <a class="next" onclick="changeSlide(1)">❯</a>
            </div> 
            <div style="display: flex; justify-content: center; width: 100%; margin-top: 2rem;">
                <a href="#benefits" class="btn btn-primary">Explore</a>
            </div>
        </div>

        <!-- Benefits Section -->
        <div id="benefits" class="benefits-section">
            <div class="benefits-image">
                <img src="/static/img/phhi4.jpg" alt="KwikCare No HMO? No Problem!">
            </div>
            <div class="benefits-content">
                <h2>GigGenius <span>is an official affliate partner of Kwik Care</span></h2>
                <p>Start protecting your growing family's health with KwikCare!</p>
                
                <p>Why settle for traditional corporate HMOs when you can give them KwikCare's 2-in-1 HMO plan: comprehensive coverage + exclusive lifestyle rewards! ⭐</p>
                
                <ul class="checklist">
                    <li>100% digital application and real-time approval.</li>
                    <li>Comprehensive plans starting at ₱995/mo plus rewards.</li>
                    <li>Fast and easy employee onboarding.</li>
                    <li>Billed monthly for easier finance tracking.</li>
                    <li>No lock-in period.</li>
                    <li>Access to 1,800 hospitals and clinics and 55,000 physician.</li>
                </ul>
                <a href="https://kwik.insure/kwikcare" target="_blank" class="btn btn-gigs" style="margin-top: 1rem;">Register Now</a>
            </div>
        </div>
        
        <!-- Features Section with 3 images -->
        <div class="features-section">
            <div class="feature-card">
                <img src="/static/img/phhi6.jpg" alt="Affordable HMO for your family">
            </div>
            <div class="feature-card">
                <img src="/static/img/phhi5.jpg" alt="A 2-in-1 HMO that your employees will love">
            </div>
            <div class="feature-card">
                <img src="/static/img/phhi7.jpg" alt="KwikCare Health Director Plans">
            </div>
        </div>
    </div>

    <!-- Benefits Section -->
    <div id="benefits" class="benefits-section">
        <div class="benefits-content" style="width: 100%; text-align: center;">
            <div class="kwikcare-image" style="margin: 0 auto; max-width: 300px;">
                <img src="/static/img/phhi1.png" alt="KwikCare Affordable Health Plans" style="width: 100%; height: auto;">
            </div>
            
            <div style="display: flex; justify-content: space-around; margin: 3rem 0; flex-wrap: wrap; gap: 2rem;">
                <div style="flex: 1; min-width: 250px; padding: 2rem; border-radius: 15px; background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('/static/img/phhi8.jpg'); background-size: cover; background-position: center; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <i class="bi bi-person-plus-fill" style="font-size: 4rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                    <h3 style="font-size: 2rem; color: var(--primary-pink)">1. Sign Up</h3>
                    <p style="font-size: 1.2rem; font-weight: bold;">Start protecting yourself or your family at KwikCare Insurance.</p>
                </div>
                
                <div style="flex: 1; min-width: 250px; padding: 2rem; border-radius: 15px; background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('/static/img/phhi9.jpg'); background-size: cover; background-position: center; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <i class="bi bi-cart-check-fill" style="font-size: 4rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                    <h3 style="font-size: 2rem; color: var(--primary-pink)">2. Buy Products</h3>
                    <p style="font-size: 1.2rem; font-weight: bold;">KwickCare Insurance offers variety of insurance products. Choose the one that best suit your needs.</p>
                </div>
                
                <div style="flex: 1; min-width: 250px; padding: 2rem; border-radius: 15px; background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('/static/img/phhi10.jpg'); background-size: cover; background-position: center; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <i class="bi bi-shield-check" style="font-size: 4rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                    <h3 style="font-size: 2rem; color: var(--primary-pink)">3. Use code</h3>
                    <p style="font-size: 1.2rem; font-weight: bold;">Ready to protect yourself, use the referral code <span style="color: var(--primary-pink)">"hlcantos"</span></p>
                </div>
            </div>
            <a href="https://kwik.insure/kwikcare" target="_blank" class="btn btn-primary" style="margin-bottom: 1.5rem;">Sign-Up Now</a>
            <p style="width: 100%; text-align: center; margin-bottom: 1rem;">Like & Visit this link:</p>
            <div class="social-links" style="justify-content: center; margin-bottom: 2rem;">
                <a href="https://kwik.insure/kwikcare" target="_blank" class="social-link">
                    <i class="bi bi-globe"></i>
                    <span>Website</span>
                </a>
                <a href="https://www.facebook.com/kwikinsure" target="_blank" class="social-link">
                    <i class="bi bi-facebook"></i>
                    <span>Facebook</span>
                </a>
                <a href="https://www.youtube.com/@kwik.insure3269" target="_blank" class="social-link">
                    <i class="bi bi-youtube"></i>
                    <span>Youtube</span>
                </a>
                <a href="https://www.instagram.com/kwikinsure/" target="_blank" class="social-link">
                    <i class="bi bi-instagram"></i>
                    <span>Instagram</span>
                </a>
                <a href="https://www.tiktok.com/@kwik.insure0" target="_blank" class="social-link">
                    <i class="bi bi-tiktok"></i>
                    <span>Tiktok</span>
                </a>
                <a href="https://www.linkedin.com/company/kwikinsure/" target="_blank" class="social-link">
                    <i class="bi bi-linkedin"></i>
                    <span>Linkedin</span>
                </a>
            </div>
            <p style="font-size: 0.9rem; color: #666; margin-top: 2rem;">© Kwiktech Insurance Brokerage Inc. is supervised by the Insurance Commission under license No. IB-05-2024-R.</p>
        </div>
    </div>

    <footer>
        <div class="footer-grid">
            <div class="footer-column">
                <h3>For Clients</h3>
                <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                <a href="{{ url_for('marketplace') }}">Marketplace</a>
                <a href="{{ url_for('payroll_services') }}">Payroll Services</a>
                <a href="{{ url_for('service_catalog') }}">Service Catalog</a>
                <a href="{{ url_for('business_networking') }}">Business Networking</a>
                <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
            </div>
            <div class="footer-column">
                <h3>For Geniuses</h3>
                <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                <a href="{{ url_for('direct_contracts') }}">Direct Contracts</a>
                <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                <a href="{{ url_for('mentor_application') }}">Mentor Application</a>
                <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                <a href="{{ url_for('news_and_events') }}">News & Events</a>
                <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
            </div>
            <div class="footer-column">
                <h3>Company</h3>
                <a href="{{ url_for('about_us') }}">About Us</a>
                <a href="{{ url_for('contact_us') }}">Contact Us</a>
                <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>Follow Us:
                <span class="social-icons">
                    <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                    <a href="https://www.instagram.com/giggenius.io/" class="bi bi-instagram"></a>
                    <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                    <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                    <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                    <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                </span>
            </p>
            <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            <p>
                <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> | 
                <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
            </p>
        </div>
    </footer>

    <script>

        let slideIndex = 0;
            showSlides();

            function changeSlide(n) {
                slideIndex += n - 1;
                showSlides();
            }

            function showSlides() {
                let slides = document.getElementsByClassName("slide");
                
                slideIndex++;
                if (slideIndex > slides.length) {slideIndex = 1}
                if (slideIndex < 1) {slideIndex = slides.length}
                
                for (let i = 0; i < slides.length; i++) {
                    slides[i].style.display = "none";  
                }
                
                slides[slideIndex-1].style.display = "block";  
                setTimeout(showSlides, 6000); // Increased from 4000 to 6000 (6 seconds)
            }

            document.querySelector('a[href="#benefits"]').addEventListener('click', function(e) {
            e.preventDefault();
            const benefitsSection = document.getElementById('benefits');
            
            // Scroll to the benefits section
            benefitsSection.scrollIntoView({ behavior: 'smooth' });
            
            // Add highlight effect
            benefitsSection.style.transition = 'background-color 0.3s ease';
            benefitsSection.style.backgroundColor = '#f0f7ff';  // Light blue highlight
            
            // Remove highlight after 1.5 seconds
            setTimeout(() => {
                benefitsSection.style.backgroundColor = 'white';
            }, 1500);
        });
        
        function toggleMenu() {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('active');
        }
        let selectedRole = null;

        function closeAllModals() {
            // Get all elements with class "modal" and hide them
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            // Restore scrolling
            document.body.style.overflow = 'auto';
        }

        function openModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';
            
            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
            
        }

        function continueToRegistration() {
            showVerificationModal();
        }

        function openLoginModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function openForgotPasswordModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }

        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            // Add your forgot password logic here
            closeForgotPasswordModal();
        }

        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            
            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');

        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });

        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for freelancers...',
                    gigs: 'Search for gigs...',
                    client: 'Search for clients...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch("{{ url_for('login') }}", {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.requireVerification) {
                    // Show security code verification modal for admin
                    openSecurityCodeModal();
                    // Display the email in the verification modal
                    document.getElementById('securityCodeEmail').textContent = data.email;
                    // Set a flag to indicate this is admin verification
                    window.selectedRole = null; // Clear any role selection
                    window.isAdminVerification = true;
                } else {
                    // Regular user redirect
                    window.location.href = data.redirect;
                }
            } else {
                // Show error message
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = data.error;
                errorMessage.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const errorMessage = document.getElementById('loginErrorMessage');
            errorMessage.textContent = "An error occurred during login. Please try again.";
            errorMessage.style.display = 'block';
        });
    });

    function openSecurityCodeModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';
    }

    function closeSecurityCodeModal() {
        document.getElementById('securityCodeModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function sendSecurityCode() {
        // Skip actual code sending and just show the code input
        document.getElementById('sendCodeBtn').style.display = 'none';
        document.getElementById('codeInputContainer').style.display = 'block';
        
        // Focus on first input
        document.querySelector('.code-input[data-index="1"]').focus();
        
        // Add event listeners for code inputs
        setupCodeInputs();
    }

    function setupCodeInputs() {
        const inputs = document.querySelectorAll('.code-input');
        
        inputs.forEach((input, index) => {
            input.addEventListener('keyup', function(e) {
                // If a number is entered
                if (/^[0-9]$/.test(e.key)) {
                    // Move to next input if available
                    if (index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                }
                // Handle backspace
                else if (e.key === 'Backspace') {
                    // Move to previous input if available and current is empty
                    if (index > 0 && input.value === '') {
                        inputs[index - 1].focus();
                    }
                }
            });
            
            // Handle paste event
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text');
                if (/^\d+$/.test(pastedData)) {
                    // Fill inputs with pasted digits
                    for (let i = 0; i < Math.min(pastedData.length, inputs.length); i++) {
                        inputs[i].value = pastedData[i];
                    }
                    // Focus on the next empty input or the last one
                    const nextEmptyIndex = Array.from(inputs).findIndex(input => !input.value);
                    if (nextEmptyIndex !== -1) {
                        inputs[nextEmptyIndex].focus();
                    } else {
                        inputs[inputs.length - 1].focus();
                    }
                }
            });
        });
    }

    function verifySecurityCode() {
        // Get email from the form
        let email;
        const emailInput = document.getElementById('registrationEmail');
        if (emailInput) {
            email = emailInput.value;
        } else {
            email = document.getElementById('securityCodeEmail').textContent;
        }
        
        // Get the code from inputs
        const codeInputs = document.querySelectorAll('.code-input');
        let securityCode = '';
        codeInputs.forEach(input => {
            securityCode += input.value || '0'; // Use '0' for any empty input
        });
        
        // Create form data for verification
        const formData = new FormData();
        formData.append('email', email);
        formData.append('code', securityCode);
        
        // Determine which endpoint to use based on context
        const isAdminVerification = window.isAdminVerification === true;
        const endpoint = isAdminVerification ? "{{ url_for('verify_admin') }}" : "{{ url_for('verify_email') }}";
        
        // Send verification request to server
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (isAdminVerification) {
                    // Admin verification successful - redirect to admin page
                    window.location.href = data.redirect;
                } else {
                    // Registration verification - proceed to registration
                    closeSecurityCodeModal();
                    // Redirect to appropriate registration page
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            } else {
                // For testing purposes, proceed anyway
                if (isAdminVerification) {
                    window.location.href = "{{ url_for('admin_page') }}";
                } else {
                    closeSecurityCodeModal();
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // For testing purposes, proceed anyway
            if (window.isAdminVerification) {
                window.location.href = "{{ url_for('admin_page') }}";
            } else {
                closeSecurityCodeModal();
                if (window.selectedRole === 'genius') {
                    window.location.href = "{{ url_for('genius_registration') }}";
                } else {
                    window.location.href = "{{ url_for('client_registration') }}";
                }
            }
        });
    }

    function resendSecurityCode() {
        // Reset the code inputs
        document.querySelectorAll('.code-input').forEach(input => {
            input.value = '';
        });
        
        // Hide error message if shown
        document.getElementById('codeErrorMessage').style.display = 'none';
        
        // Show sending state
        const resendLink = document.querySelector('.resend-code a');
        const originalText = resendLink.textContent;
        resendLink.textContent = 'Sending...';
        resendLink.style.pointerEvents = 'none';
        
        setTimeout(() => {
            // Restore link text
            resendLink.textContent = originalText;
            resendLink.style.pointerEvents = 'auto';
            
            // Focus on first input
            document.querySelector('.code-input[data-index="1"]').focus();
        }, 1500);
    }

    function closeVerificationModal() {
        document.getElementById('verificationModal').style.display = 'none';
    }
    
    function showVerificationModal() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;
        const roleMessage = document.getElementById('roleMessage');
        
        if (!geniusRole && !clientRole) {
            roleMessage.style.display = 'block';
            return;
        }
        
        roleMessage.style.display = 'none';
        const verificationMessage = document.getElementById('verificationMessage');
        
        if (geniusRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Genius (Freelancer)</strong>. Is this correct?';
        } else if (clientRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Client (Business Owner)</strong>. Is this correct?';
        }
        
        document.getElementById('joinModal').style.display = 'none';
        document.getElementById('verificationModal').style.display = 'flex';
    }
    
    function proceedToRegistration() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;
        
        // Show security code verification before proceeding to registration
        if (geniusRole || clientRole) {
            // Store the selected role in a variable for later use
            window.selectedRole = geniusRole ? 'genius' : 'client';
            
            // Close verification modal and open security code modal
            document.getElementById('verificationModal').style.display = 'none';
            openRegistrationSecurityModal();
        }
    }

    function openRegistrationSecurityModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';
        
        // Update modal title and description for registration context
        document.querySelector('#securityCodeModal h2').textContent = 'Verify Your Email';
        document.querySelector('#securityCodeModal p').textContent = 'We\'ll send a security code to verify your email before registration.';
        
        // Get email from the join modal if available
        const emailInput = document.querySelector('#joinModal input[type="email"]');
        if (emailInput && emailInput.value) {
            document.getElementById('securityCodeEmail').textContent = emailInput.value;
        } else {
            // If no email is available, show an input field
            document.getElementById('securityCodeEmail').innerHTML = '<input type="email" id="registrationEmail" placeholder="Enter your email" required>';
        }
    }
    </script>    
</body>
</html>
