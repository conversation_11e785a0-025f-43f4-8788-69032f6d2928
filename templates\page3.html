<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - GigGenius 2025</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/job-details-2025.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>

    <div class="container">
        <div class="job-details-container">
            <h1 class="page-title">Create Your Job</h1>

            <!-- Title Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Title</h2>
                    <button class="edit-button" onclick="openEditModal('title')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <p id="job-title">{{ title }}</p>
                <div class="job-status draft">Draft</div>
            </div>

            <!-- Category Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Category</h2>
                    <button class="edit-button" onclick="openEditModal('category')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="category-details">
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-folder"></i> Category</span>
                        <span class="meta-value" id="job-category">{{ job_category }}</span>
                    </div>
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-tag"></i> Specialty</span>
                        <span class="meta-value" id="job-specialty">{{ specialty }}</span>
                    </div>
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-briefcase"></i> Job Type</span>
                        <span class="meta-value" id="job-type">{{ job_type }}</span>
                    </div>
                </div>
            </div>

            <!-- Description Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Description</h2>
                    <button class="edit-button" onclick="openEditModal('description')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="form-group">
                    <textarea id="job-description" class="form-control" rows="5" placeholder="Describe your job requirements and expectations..." data-session-value="{{ session.get('description', '') }}">{{ session.get('description', '') }}</textarea>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Required Skills</h2>
                    <button class="edit-button" onclick="openEditModal('skills')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div id="skills-container">
                    <div class="skills-list" data-session-skills="{{ session.get('skills', '') }}">
                        <!-- Skills will be added here when the user selects them -->
                    </div>
                </div>
            </div>

            <!-- Scope Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Project Scope</h2>
                    <button class="edit-button" onclick="openEditModal('scope')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="scope-details">
                    <div class="scope-description">
                        <h3>Description</h3>
                        <p id="job-scope-description">{{ session.get('project_description', '') }}</p>
                    </div>

                    <div class="scope-meta">
                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-chart-bar"></i> Project Size</span>
                            <span class="meta-value" id="job-scope-size">{{ session.get('project_size', '0') }}</span>
                        </div>

                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-calendar-alt"></i> Duration</span>
                            <span class="meta-value" id="job-scope-duration">{{ session.get('duration', '0') }}</span>
                        </div>

                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-user-graduate"></i> Experience Level</span>
                            <span class="meta-value" id="job-scope-experience">{{ session.get('experience_level', '0') }}</span>
                        </div>

                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-users"></i> Hiring Preference</span>
                            <span class="meta-value" id="job-scope-hiring">{{ session.get('hiring_preference', '0') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Budget Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Budget</h2>
                    <button class="edit-button" onclick="openEditModal('budget')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="budget-details">
                    <p id="job-budget">{% if session.get('budget_type') == 'hourly' %}${{ session.get('budget_amount', '0') }} (Hourly Rate){% else %}${{ session.get('budget_amount', '0') }}{% if session.get('budget_amount') %} (Fixed Price){% endif %}{% endif %}</p>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="submit-buttons">
                {% if not session.get('edit_job_id') %}
                <button class="btn-secondary" onclick="submitJob('draft')">
                    <i class="fas fa-save"></i> Draft Post
                </button>
                {% endif %}
                <button class="btn-primary" onclick="submitJob('publish')">
                    <i class="fas fa-paper-plane"></i> {% if session.get('edit_job_id') %}Update Job{% else %}Post Job{% endif %}
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Title Modal -->
    <div id="titleModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Title</h2>
            <div class="form-group">
                <label for="edit-title">Job Title</label>
                <input type="text" id="edit-title" class="form-control" value="{{ title }}">
            </div>
            <button class="btn-primary" onclick="saveEdit('title')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('titleModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Category</h2>

            <!-- Category Selection -->
            <div class="form-group">
                <label for="category-selection-type">Category Selection</label>
                <div class="selection-type-toggle">
                    <button type="button" class="selection-toggle active" onclick="toggleCategorySelectionType('predefined')">Choose from list</button>
                    <button type="button" class="selection-toggle" onclick="toggleCategorySelectionType('custom')">Enter custom</button>
                </div>
            </div>

            <!-- Predefined Category Selection -->
            <div id="predefined-category" class="form-group">
                <label for="edit-category">Job Category</label>
                <select id="edit-category" class="form-control" onchange="updateSpecialties()">
                    <option value="Graphic Design" {% if job_category == 'Graphic Design' %}selected{% endif %}>Graphic Design</option>
                    <option value="General Translation Services" {% if job_category == 'General Translation Services' %}selected{% endif %}>General Translation Services</option>
                    <option value="Social Media Marketing" {% if job_category == 'Social Media Marketing' %}selected{% endif %}>Social Media Marketing</option>
                    <option value="Web Development" {% if job_category == 'Web Development' %}selected{% endif %}>Web Development</option>
                    <option value="Content Writing" {% if job_category == 'Content Writing' %}selected{% endif %}>Content Writing</option>
                    <option value="AI & Machine Learning" {% if job_category == 'AI & Machine Learning' %}selected{% endif %}>AI & Machine Learning</option>
                    <option value="Virtual Reality Design" {% if job_category == 'Virtual Reality Design' %}selected{% endif %}>Virtual Reality Design</option>
                    <option value="Blockchain Development" {% if job_category == 'Blockchain Development' %}selected{% endif %}>Blockchain Development</option>
                </select>
            </div>

            <!-- Custom Category Input -->
            <div id="custom-category" class="form-group" style="display: none;">
                <label for="custom-category-input">Custom Job Category</label>
                <input type="text" id="custom-category-input" class="form-control" placeholder="Enter your own category">
            </div>

            <!-- Specialty Selection -->
            <div class="form-group">
                <label for="specialty-selection-type">Specialty Selection</label>
                <div class="selection-type-toggle">
                    <button type="button" class="selection-toggle active" onclick="toggleSpecialtySelectionType('predefined')">Choose from list</button>
                    <button type="button" class="selection-toggle" onclick="toggleSpecialtySelectionType('custom')">Enter custom</button>
                </div>
            </div>

            <!-- Predefined Specialty Selection -->
            <div id="predefined-specialty" class="form-group">
                <label for="edit-specialty">Specialty</label>
                <select id="edit-specialty" class="form-control">
                    <!-- Graphic Design Specialties -->
                    <optgroup label="Graphic Design" class="specialty-group" data-category="Graphic Design">
                        <option value="Logo Design">Logo Design</option>
                        <option value="Brand Identity Design">Brand Identity Design</option>
                        <option value="Illustration">Illustration</option>
                        <option value="UI/UX Design">UI/UX Design</option>
                        <option value="Packaging Design">Packaging Design</option>
                        <option value="Print Design">Print Design</option>
                    </optgroup>

                    <!-- Translation Specialties -->
                    <optgroup label="Translation" class="specialty-group" data-category="General Translation Services">
                        <option value="Document Translation">Document Translation</option>
                        <option value="Website Localization">Website Localization</option>
                        <option value="Technical Translation">Technical Translation</option>
                        <option value="Legal Translation">Legal Translation</option>
                        <option value="Medical Translation">Medical Translation</option>
                    </optgroup>

                    <!-- Marketing Specialties -->
                    <optgroup label="Marketing" class="specialty-group" data-category="Social Media Marketing">
                        <option value="Content Marketing">Content Marketing</option>
                        <option value="Social Media Management">Social Media Management</option>
                        <option value="Email Marketing">Email Marketing</option>
                        <option value="SEO">SEO</option>
                        <option value="PPC Advertising">PPC Advertising</option>
                    </optgroup>

                    <!-- Web Development Specialties -->
                    <optgroup label="Web Development" class="specialty-group" data-category="Web Development">
                        <option value="Frontend Development">Frontend Development</option>
                        <option value="Backend Development">Backend Development</option>
                        <option value="Full Stack Development">Full Stack Development</option>
                        <option value="E-commerce Development">E-commerce Development</option>
                        <option value="WordPress Development">WordPress Development</option>
                    </optgroup>

                    <!-- Content Writing Specialties -->
                    <optgroup label="Content Writing" class="specialty-group" data-category="Content Writing">
                        <option value="Blog Writing">Blog Writing</option>
                        <option value="Copywriting">Copywriting</option>
                        <option value="Technical Writing">Technical Writing</option>
                        <option value="Creative Writing">Creative Writing</option>
                        <option value="SEO Writing">SEO Writing</option>
                    </optgroup>

                    <!-- AI & ML Specialties -->
                    <optgroup label="AI & Machine Learning" class="specialty-group" data-category="AI & Machine Learning">
                        <option value="Natural Language Processing">Natural Language Processing</option>
                        <option value="Computer Vision">Computer Vision</option>
                        <option value="Predictive Analytics">Predictive Analytics</option>
                        <option value="Chatbot Development">Chatbot Development</option>
                        <option value="Deep Learning">Deep Learning</option>
                    </optgroup>

                    <!-- VR Design Specialties -->
                    <optgroup label="VR Design" class="specialty-group" data-category="Virtual Reality Design">
                        <option value="3D Modeling">3D Modeling</option>
                        <option value="VR Environment Design">VR Environment Design</option>
                        <option value="AR Experience Design">AR Experience Design</option>
                        <option value="Interactive VR">Interactive VR</option>
                    </optgroup>

                    <!-- Blockchain Specialties -->
                    <optgroup label="Blockchain" class="specialty-group" data-category="Blockchain Development">
                        <option value="Smart Contract Development">Smart Contract Development</option>
                        <option value="DApp Development">DApp Development</option>
                        <option value="Cryptocurrency">Cryptocurrency</option>
                        <option value="NFT Development">NFT Development</option>
                    </optgroup>
                </select>
            </div>

            <!-- Custom Specialty Input -->
            <div id="custom-specialty" class="form-group" style="display: none;">
                <label for="custom-specialty-input">Custom Specialty</label>
                <input type="text" id="custom-specialty-input" class="form-control" placeholder="Enter your own specialty">
            </div>

            <button class="btn-primary" onclick="saveEdit('category')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('categoryModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Description Modal -->
    <div id="descriptionModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Description</h2>
            <div class="form-group">
                <label for="edit-description">Job Description</label>
                <textarea id="edit-description" class="form-control" rows="8" placeholder="Provide detailed information about the job requirements, responsibilities, and qualifications..."></textarea>
            </div>
            <button class="btn-primary" onclick="saveEdit('description')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('descriptionModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Skills Modal -->
    <div id="skillsModal" class="modal">
        <div class="modal-content">
            <h2>Edit Required Skills</h2>
            <div class="form-group">
                <label for="skills-input">Add Skills</label>
                <div class="skills-input-container">
                    <input type="text" id="skills-input" class="form-control" placeholder="Type a skill and press Enter">
                    <button type="button" id="add-skill-btn" class="add-skill-btn"><i class="fas fa-plus"></i></button>
                </div>
                <p class="input-help">Type a skill and press Enter or click the + button to add it</p>
            </div>
            <div class="form-group">
                <label>Selected Skills</label>
                <div id="edit-skills-list" class="skills-edit-list">
                    <!-- Selected skills will appear here -->
                </div>
                <p class="input-help">Click on a skill to remove it</p>
            </div>
            <div class="form-group">
                <label>Suggested Skills</label>
                <div class="skills-suggestions">
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('JavaScript')">JavaScript</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Python')">Python</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('React')">React</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Node.js')">Node.js</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('UI/UX Design')">UI/UX Design</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Machine Learning')">Machine Learning</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Data Analysis')">Data Analysis</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('AWS')">AWS</span>
                </div>
            </div>
            <button class="btn-primary" onclick="saveEdit('skills')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('skillsModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Scope Modal -->
    <div id="scopeModal" class="modal">
        <div class="modal-content">
            <h2>Edit Project Scope</h2>
            <div class="form-group">
                <label for="edit-scope">Project Description</label>
                <textarea id="edit-scope" class="form-control" rows="4" placeholder="Define the boundaries and deliverables of your project...">{{ session.get('project_description', '') }}</textarea>
            </div>

            <div class="form-group">
                <label>Project Size</label>
                <div class="scope-size-options">
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="small" id="size-small" {% if session.get('project_size') and session.get('project_size', '').lower() == 'small' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-pie"></i>
                            <span>Small</span>
                            <small>Simple task, quick turnaround</small>
                        </span>
                    </label>
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="medium" id="size-medium" {% if session.get('project_size') and session.get('project_size', '').lower() == 'medium' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-bar"></i>
                            <span>Medium</span>
                            <small>Standard complexity, moderate timeline</small>
                        </span>
                    </label>
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="large" id="size-large" {% if session.get('project_size') and session.get('project_size', '').lower() == 'large' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-line"></i>
                            <span>Large</span>
                            <small>Complex project, extended timeline</small>
                        </span>
                    </label>
                </div>
            </div>

            <div class="scope-flex-row">
                <div class="form-group flex-1">
                    <label>Duration</label>
                    <div class="scope-duration">
                        <div class="duration-input">
                            <input type="number" id="scope-duration-value" class="form-control" min="0" value="{% if session.get('duration') and ' ' in session.get('duration') %}{{ session.get('duration').split(' ')[0] }}{% else %}0{% endif %}">
                            <select id="scope-duration-unit" class="form-control">
                                <option value="">Select unit</option>
                                <option value="days" {% if session.get('duration') and 'days' in session.get('duration').lower() %}selected{% endif %}>Days</option>
                                <option value="weeks" {% if session.get('duration') and 'weeks' in session.get('duration').lower() %}selected{% endif %}>Weeks</option>
                                <option value="months" {% if session.get('duration') and 'months' in session.get('duration').lower() %}selected{% endif %}>Months</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group flex-1">
                    <label>Experience Level</label>
                    <select id="scope-experience" class="form-control">
                        <option value="">Select experience level</option>
                        <option value="entry" {% if session.get('experience_level') and session.get('experience_level').lower() == 'entry' %}selected{% endif %}>Entry Level</option>
                        <option value="intermediate" {% if session.get('experience_level') and session.get('experience_level').lower() == 'intermediate' %}selected{% endif %}>Intermediate</option>
                        <option value="expert" {% if session.get('experience_level') and session.get('experience_level').lower() == 'expert' %}selected{% endif %}>Expert</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label>Hiring Preference</label>
                <select id="scope-hiring" class="form-control">
                    <option value="">Select hiring preference</option>
                    <option value="individual" {% if session.get('hiring_preference') and session.get('hiring_preference').lower() == 'individual' %}selected{% endif %}>Individual Freelancer</option>
                    <option value="agency" {% if session.get('hiring_preference') and session.get('hiring_preference').lower() == 'agency' %}selected{% endif %}>Agency</option>
                    <option value="any" {% if session.get('hiring_preference') and session.get('hiring_preference').lower() == 'any' %}selected{% endif %}>No Preference</option>
                </select>
            </div>

            <button class="btn-primary" onclick="saveEdit('scope')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('scopeModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Budget Modal -->
    <div id="budgetModal" class="modal">
        <div class="modal-content">
            <h2>Edit Budget</h2>
            <div class="form-group">
                <label>Budget Type</label>
                <div class="budget-type-options">
                    <label class="budget-type-option">
                        <input type="radio" name="budget-type" value="hourly" id="budget-hourly" {% if session.get('budget_type') == 'hourly' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-clock"></i>
                            <span>Hourly Rate</span>
                        </span>
                    </label>
                    <label class="budget-type-option">
                        <input type="radio" name="budget-type" value="fixed" id="budget-fixed" {% if session.get('budget_type') != 'hourly' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Fixed Price</span>
                        </span>
                    </label>
                </div>
            </div>
            <div id="hourly-rate-fields">
                <div class="form-group">
                    <label for="budget-min">Minimum Hourly Rate ($)</label>
                    <input type="number" id="budget-min" class="form-control" value="0" min="0">
                </div>
                <div class="form-group">
                    <label for="budget-max">Maximum Hourly Rate ($)</label>
                    <input type="number" id="budget-max" class="form-control" value="0" min="0">
                </div>
                <p class="budget-help-text">Set a range for hourly rates. Actual payment will depend on hours worked.</p>
            </div>

            <div id="fixed-price-fields">
                <div class="form-group">
                    <label for="budget-fixed-amount">Fixed Price Amount ($)</label>
                    <input type="number" id="budget-fixed-amount" class="form-control" value="{{ session.get('budget_amount', '0') }}" min="0">
                </div>
                <p class="budget-help-text">Set an exact amount you'll pay for the entire project.</p>
            </div>
            <button class="btn-primary" onclick="saveEdit('budget')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('budgetModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/page3.js') }}"></script>
</body>

</html>