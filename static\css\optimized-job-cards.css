/* Optimized Job Cards Styling */

/* ONLY ONE CARD PER ROW - NO GRID OR FLEX */
.jobs-list {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
}

/* Job card styling - Optimized */
.job-card {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    display: flex !important;
    flex-direction: column !important;
    position: relative;
    width: 100% !important;
    margin-bottom: 15px !important;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    float: none !important;
    clear: both !important;
}

/* Job header */
.job-header {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e5e7eb;
}

/* Client avatar */
.client-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Job title client */
.job-title-client {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.client-info {
    display: flex;
    flex-direction: column;
    margin-bottom: 0.5rem;
}

.client-profile {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.5rem;
    border-left: 2px solid #004AAD;
}

.job-title-container {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
}

.job-title-client h2 {
    font-size: 1.1rem;
    color: #333;
    margin: 0;
    font-weight: 600;
    line-height: 1.3;
}

.client-name {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.client-name-text {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    padding-left: 0.5rem;
}

.client-name-text:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: #004AAD;
    border-radius: 50%;
}

.client-full-name {
    font-size: 0.8rem;
    color: #6b7280;
    margin-left: 0.5rem;
}

.client-location {
    margin-top: 0.25rem;
    margin-left: 0.5rem;
}

.location-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.2rem 0.5rem;
    background-color: #f0f7ff;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #004AAD;
    font-weight: 500;
}

.location-badge i {
    margin-right: 0.25rem;
    font-size: 0.75rem;
    color: #004AAD;
}

/* Job body */
.job-body {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

/* Job description */
.job-description {
    color: #4b5563;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
    font-size: 0.9rem;
    padding-left: 0.5rem;
    border-left: 2px solid #e5e7eb;
}

/* Job meta */
.job-meta {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.job-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #4b5563;
    padding: 0.4rem 0.5rem;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #e5e7eb;
}

.job-meta-item.highlight {
    background-color: #f0f7ff;
    border-left: 2px solid #004AAD;
    font-weight: 500;
}

.job-meta-item i {
    color: #004AAD;
    width: 16px;
    height: 16px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f7ff;
    border-radius: 50%;
    padding: 0.5rem;
}

.job-meta-item span {
    font-weight: 500;
}

.value-text {
    color: #004AAD;
    font-weight: 500;
}

/* Job tags section */
.job-tags-section {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.tags-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e5e7eb;
}

.job-tags {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.job-tag {
    background-color: #f0f7ff;
    color: #004AAD;
    padding: 0.4rem 0.75rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid #e5e7eb;
    display: inline-flex;
    align-items: center;
}

.tag-label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: #333;
}

.skills-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
    grid-column: 1 / -1;
}

.skills-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.skills-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background-color: #f0f7ff;
    border: 1px solid #e5e7eb;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: #004AAD;
    font-weight: 500;
    font-size: 0.8rem;
}

/* Job date */
.job-date {
    font-size: 0.8rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid #e5e7eb;
}

.job-date i {
    color: #004AAD;
}

/* Job footer */
.job-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.job-price-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.budget-label {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
}

.job-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #004AAD;
}

.budget-type {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
}

.view-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: #004AAD;
    color: white;
    font-weight: 600;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

/* Removed hover effect */

/* Responsive adjustments */
@media (max-width: 768px) {
    .job-meta {
        grid-template-columns: repeat(2, 1fr);
    }
}
