// JavaScript for handling job submission form
document.addEventListener('DOMContentLoaded', function() {
    const jobForm = document.getElementById('jobSubmissionForm');
    
    if (jobForm) {
        jobForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(jobForm);
            const submitButton = jobForm.querySelector('button[type="submit"]');
            
            // Disable button and show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Submitting...';
            
            // Submit the form
            fetch('/submit_job', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Job posted successfully!');
                    
                    // Redirect to client page if redirect URL is provided
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        // Fallback to client_page if no redirect URL is provided
                        window.location.href = '/client_page';
                    }
                } else {
                    // Show error message
                    alert('Error: ' + (data.error || 'An error occurred'));
                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Post Job';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
                // Reset button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Post Job';
            });
        });
    }
});
